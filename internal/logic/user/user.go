package user

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/log"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/utils/constack/tools"
	"mlops/internal/consts"
	errors2 "mlops/internal/consts/errors"
	"mlops/internal/dao"
	"mlops/internal/model/dto"
	"mlops/internal/model/entity"
	"mlops/internal/service"
	"time"
)

type sUser struct {
}

func init() {
	service.RegisterUser(newsUser())
}

func newsUser() *sUser {
	return &sUser{}
}

func (this *sUser) Table() string {
	return dao.User.Table()
}

func (this *sUser) GetUserById(ctx context.Context, id int) (user *entity.User, err error) {
	user = &entity.User{}
	err = dao.User.Ctx(ctx).Where("id", id).Scan(user)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			// not found
			return nil, errors2.ErrUserNotFound
		}
		return nil, err
	}
	return user, nil
}

func (this *sUser) CreateUser(ctx context.Context, user *entity.User) (err error) {
	_, err = dao.User.Ctx(ctx).Data(user).Insert()
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			// not found

		}
		return err
	}
	return
}

func (this *sUser) GetUserByUsername(ctx context.Context, username string) (user *entity.User, err error) {
	user = &entity.User{}

	where := g.Map{
		dao.User.Columns().Username: username,
	}
	err = dao.User.Ctx(ctx).Where(where).Scan(user)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, errors2.ErrUserNotFound
		}
		return nil, gerror.Wrap(err, "查询用户失败")
	}
	return user, nil
}

func (this *sUser) CheckIsActive(ctx context.Context, uid int) error {
	cnt, err := dao.User.Ctx(ctx).Cache(gdb.CacheOption{
		Duration: time.Second * 60,
		Name:     fmt.Sprintf("%d", uid),
	}).Where(g.Map{"id": uid, "is_active": consts.STrue}).Count()
	if err != nil {
		return fmt.Errorf("check user active error. err: %s", err.Error())
	}

	if cnt == 0 {
		return fmt.Errorf("user is not active")
	}
	return nil
}

func (this *sUser) GetUserWithCondition(ctx context.Context, condition g.Map) (user *entity.User, err error) {
	user = &entity.User{}

	err = dao.User.Ctx(ctx).Where(condition).Scan(user)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, errors2.ErrUserNotFound
		}
		return nil, gerror.Wrap(err, "查询用户失败")
	}
	return
}

func (this *sUser) GetUserWithUserNamePassword(ctx context.Context, username, password string) (user *entity.User, err error) {
	user = &entity.User{}

	cipherPass, err := tools.EncryptData(password, consts.DefaultCipherKey)
	if err != nil {
		log.L.WithName("sUser.GetUserWithUserNamePassword").Errorf(ctx, "encrypt password error:%s", err.Error())
		return nil, err
	}

	err = dao.User.Ctx(ctx).Where(g.Map{
		dao.User.Columns().Username: username,
		dao.User.Columns().Password: cipherPass,
	}).Scan(user)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, errors2.ErrInvalidUserOrPassword
		}
		return nil, gerror.Wrap(err, "查询用户失败")
	}
	return
}

func (this *sUser) Profile(ctx context.Context, uid int) (profile *dto.UserProfile, err error) {
	profile = &dto.UserProfile{}
	userEntity, err := this.GetUserById(ctx, uid)
	if err != nil {
		return nil, err
	}

	// get auth
	authProfile, err := service.PlatFormAuth().GetUserAuthProfile(ctx, uid)
	if err != nil {
		return nil, err
	}

	profile = &dto.UserProfile{
		Uid:         userEntity.Id,
		Username:    userEntity.Username,
		ChineseName: userEntity.NickName,
		Email:       userEntity.Email,
		Role:        authProfile.Roles,
		TeamRoles:   authProfile.TeamRoles,
		EmployeeNo:  userEntity.EmployeeNo,
		CreatedAt:   userEntity.CreatedAt.Unix(),
	}

	return
}

func (this *sUser) ListUserViewObj(ctx context.Context, username string) (userViewObjs []*dto.UserViewObj, err error) {
	result := make([]*entity.User, 0)
	userViewObjs = make([]*dto.UserViewObj, 0)

	err = dao.User.Ctx(ctx).WhereLike(dao.User.Columns().Username, "%"+username+"%").Scan(&result)
	if err != nil {
		return nil, err
	}

	for _, user := range result {
		authProfile, err := service.PlatFormAuth().GetUserAuthProfile(ctx, int(user.Id))
		if err != nil {
			continue
		}

		userViewObjs = append(userViewObjs, &dto.UserViewObj{
			Id:          user.Id,
			Uid:         user.Uid,
			Name:        user.Username,
			ChineseName: user.NickName,
			Email:       user.Email,
			EmployeeNo:  user.EmployeeNo,
			Role:        authProfile.Roles,
		})
	}

	return userViewObjs, nil
}
