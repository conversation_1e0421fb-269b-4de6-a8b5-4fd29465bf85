package team

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/gogf/gf/v2/crypto/gmd5"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/log"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/resources/cmdb/api"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/utils/slice"
	"mlops/internal/consts"
	"mlops/internal/consts/auth"
	"mlops/internal/dao"
	"mlops/internal/model/do"
	"mlops/internal/model/dto"
	"mlops/internal/model/entity"
	"mlops/internal/service"
	"mlops/tools/client"
	"strings"
)

/**
team:
定时向cicd拉取团队列表 并持久化在team表中
*/

type sTeam struct {
	teamSynchronizer *teamSynchronizer
}

func init() {
	service.RegisterTeam(newsTeam())
}

func newsTeam() *sTeam {
	return &sTeam{
		teamSynchronizer: newTeamSynchronizer(),
	}
}

func (this *sTeam) Table() string {
	return dao.Team.Table()
}

func (this *sTeam) Sync(ctx context.Context) (err error) {
	return this.teamSynchronizer.start(ctx)
}

// SyncTeamOrganization 根据用户id同步组织架构团队
func (this *sTeam) SyncTeamOrganization(ctx context.Context, userId int) (pid int64, err error) {
	// 同步当前团队
	userInfo, err := service.User().GetUserById(ctx, userId)
	if err != nil {
		return 0, err
	}

	relations, err := client.CmdbApiJsonRpc.GetUserOrganizationRelation(ctx, userInfo.EmployeeNo)
	if err != nil {
		return 0, err
	}

	slice.QuickSort[*api.OrganizationRelationInfo](relations, func(a, b *api.OrganizationRelationInfo) bool {
		return a.Level < b.Level
	})

	var (
		teamName string
	)

	for _, relation := range relations {
		teamName = fmt.Sprintf("%s-%s", teamName, relation.DeptName)
	}

	teamId, err := gmd5.Encrypt(teamName)
	if err != nil {
		return 0, err
	}

	teamName = strings.Trim(teamName, "-")

	res, err := dao.Team.Ctx(ctx).Data(do.Team{
		Category: auth.TeamOrganization,
		Name:     teamName,
		TeamId:   teamId,
	}).Save()
	if err != nil {
		return 0, err
	}

	return res.LastInsertId()
}

func (this *sTeam) List(ctx context.Context) (list []*entity.Team, err error) {
	err = dao.Team.Ctx(ctx).Scan(&list)
	return
}

func (this *sTeam) ListRelatedClusterNamespace(ctx context.Context, teamId int) (res []*dto.ClusterNamespace, err error) {
	res = make([]*dto.ClusterNamespace, 0)
	val, err := service.Setting().GetVal(ctx, consts.GenerateTeamRelatedClusterNamespaceKey(teamId))
	if err != nil {
		return nil, err
	}

	log.L.WithName("sTeam.ListRelatedClusterNamespace").Infof(ctx, "team related cluster namespace: %s", val)

	err = json.Unmarshal([]byte(val), &res)
	if err != nil {
		log.L.WithName("sTeam.ListRelatedClusterNamespace").Errorf(ctx, "unmarshal team"+
			" related cluster namespace error:%s", err.Error())
		return res, err
	}

	return
}

func (this *sTeam) ListTeamUser(ctx context.Context, teamId int) (res []*dto.UserTeamRole, err error) {
	res = make([]*dto.UserTeamRole, 0)
	// 查询用户绑定的团队
	userIds := make([]int, 0)
	teamIds, err := dao.UserRoleTeamRela.Ctx(ctx).Where(dao.UserRoleTeamRela.Columns().TeamId, teamId).
		Fields(dao.UserRoleTeamRela.Columns().UserId).Array()
	if err != nil {
		return nil, err
	}

	teamEntities := make([]*entity.Team, 0, len(teamIds))
	err = dao.Team.Ctx(ctx).WhereIn(dao.Team.Columns().TeamId, teamIds).Scan(&teamEntities)
	if err != nil {
		return nil, err
	}

	for _, teamEntity := range teamEntities {
		res = append(res, &dto.UserTeamRole{
			UserId:   userId,
			TeamId:   teamEntity.TeamId,
			TeamName: teamEntity.Name,
			TeamType: auth.TeamKind(teamEntity.Category),
		})
	}

	return
}

type teamSynchronizer struct {
}

func newTeamSynchronizer() *teamSynchronizer {
	return &teamSynchronizer{}
}

func (this *teamSynchronizer) start(ctx context.Context) (err error) {
	projects, err := client.CicdAppProject.ListProject(ctx)
	if err != nil {
		return err
	}

	teamEntities := make([]*entity.Team, 0, len(projects))

	for _, project := range projects {
		teamEntities = append(teamEntities, &entity.Team{
			Category: string(auth.TeamFeature),
			Name:     project.Name,
			TeamId:   fmt.Sprintf("%d", project.Id),
		})
	}

	// 直接用cicd 团队id存储即可
	_, err = dao.Team.Ctx(ctx).Data(teamEntities).FieldsEx(dao.Team.Columns().Id).Batch(50).Save()
	if err != nil {
		return err
	}

	return
}
