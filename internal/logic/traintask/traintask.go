package traintask

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/log"
	errors2 "mlops/internal/consts/errors"
	"mlops/internal/dao"
	"mlops/internal/model/dto"
	"mlops/internal/model/entity"
	"mlops/internal/service"
	"mlops/tools/client"
	"mlops/tools/gpu"
	"sigs.k8s.io/yaml"
	"strings"
	"time"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/grand"
	rayv1 "github.com/ray-project/kuberay/ray-operator/apis/ray/v1"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
)

const (
	VolumeMountName = "vol-%d"
	ExecutionName   = "train-task-%d-%s"
	GpuCoreKey      = "52tt.com/gpu-core"
	GpuMemoryKey    = "52tt.com/gpu-memory"
	LowerLetters    = "abcdefghijklmnopqrstuvwxyz"
)

type sTrainTask struct {
}

func init() {
	service.RegisterTrainTask(newsTrainTask())
}

func newsTrainTask() *sTrainTask {
	return &sTrainTask{}
}

func (this *sTrainTask) Table() string {
	return dao.TrainTask.Table()
}

func (this *sTrainTask) ListPage(ctx context.Context, in dto.TrainTaskListInput) (pageData *dto.TrainTaskListOutput, err error) {
	list := make([]*dto.TrainTask, 0)
	q := dao.TrainTask.Ctx(ctx)
	if in.TaskName != "" {
		q = q.WhereLike(dao.TrainTask.Columns().TaskName, "%"+in.TaskName+"%")
	}
	if in.OnlyMy {
		user := service.BizCtx().Get(ctx).User
		q = q.Where(dao.TrainTask.Columns().CreatedByEmployeeNo, user.EmployeeNo)
	}
	if in.OnlyUncompleted {
		q = q.WhereIn(dao.TrainTask.Columns().LastStatus, []string{"PENDING", "RUNNING"})
	}
	if len(in.Statuses) > 0 {
		q = q.WhereIn(dao.TrainTask.Columns().LastStatus, in.Statuses)
	}

	if in.TeamId != 0 {
		q = q.Where(dao.TrainTask.Columns().TeamId, in.TeamId)
	}
	if in.CreatedByUserName != "" {
		q = q.Where(dao.TrainTask.Columns().CreatedByUserName, in.CreatedByUserName)
	}
	if in.CreatedByEmployeeNo != "" {
		q = q.Where(dao.TrainTask.Columns().CreatedByEmployeeNo, in.CreatedByEmployeeNo)
	}
	if in.UpdatedByUserName != "" {
		q = q.Where(dao.TrainTask.Columns().UpdatedByUserName, in.UpdatedByUserName)
	}
	if in.UpdatedByEmployeeNo != "" {
		q = q.Where(dao.TrainTask.Columns().UpdatedByEmployeeNo, in.UpdatedByEmployeeNo)
	}
	if len(in.CreatedAt) > 0 {
		q = q.WhereBetween(dao.TrainTask.Columns().CreatedAt, in.CreatedAt[0], in.CreatedAt[1])
	}
	if len(in.UpdatedAt) > 0 {
		q = q.WhereBetween(dao.TrainTask.Columns().UpdatedAt, in.UpdatedAt[0], in.UpdatedAt[1])
	}
	total, err := q.Count()
	if err != nil {
		return nil, err
	}
	err = q.OrderDesc(dao.TrainTask.Columns().Id).Page(in.Page, in.PageSize).Scan(&list)
	if err != nil {
		return nil, err
	}
	return &dto.TrainTaskListOutput{
		List:        list,
		Total:       total,
		CurrentPage: in.Page,
		PageSize:    in.PageSize,
	}, nil
}

func (this *sTrainTask) Get(ctx context.Context, id uint) (trainTask *dto.TrainTask, err error) {
	trainTask = &dto.TrainTask{}
	err = dao.TrainTask.Ctx(ctx).Where(dao.TrainTask.Columns().Id, id).Scan(trainTask)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, errors2.ErrTaskNotFound
		}
		return nil, err
	}

	// json to yaml
	if trainTask.TaskYaml != "" {
		yamlData, err := yaml.JSONToYAML([]byte(trainTask.TaskYaml))
		if err != nil {
			return nil, err
		}
		trainTask.TaskYaml = string(yamlData)
	}

	if trainTask.TaskType == "YAML" {
		return
	}

	trainTask.ClusterResource = dto.TrainTaskClusterResource{}
	trainTask.VolumeMounts = []*dto.TrainTaskVolumeMount{}
	err = dao.TrainTaskClusterResource.Ctx(ctx).Where(dao.TrainTaskClusterResource.Columns().TaskId, id).Scan(&trainTask.ClusterResource)
	if err != nil {
		return nil, err
	}
	err = dao.TrainTaskVolumeMount.Ctx(ctx).Where(dao.TrainTaskVolumeMount.Columns().TaskId, id).Scan(&trainTask.VolumeMounts)
	if err != nil {
		return nil, err
	}
	return
}

func (this *sTrainTask) Create(ctx context.Context, trainTask *dto.TrainTask) (err error) {
	user := service.BizCtx().Get(ctx).User
	trainTask.CreatedByUserName = user.Username
	trainTask.CreatedByEmployeeNo = user.EmployeeNo
	trainTaskEntity := toTrainTaskEntity(trainTask)
	if trainTaskEntity == nil {
		return fmt.Errorf("invalid train task")
	}
	err = dao.TrainTask.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		team := &dto.Team{}
		err = tx.Model(dao.Team.Table()).Where(dao.Team.Columns().Id, trainTask.TeamId).Scan(team)
		if err != nil {
			return err
		}
		trainTaskEntity.TeamName = team.Name
		id, err := tx.Model(dao.TrainTask.Table()).Data(trainTaskEntity).InsertAndGetId()
		if err != nil {
			return err
		}

		if trainTask.TaskType == "YAML" {
			return nil
		}

		trainTask.ClusterResource.TaskId = uint(id)
		_, err = tx.Model(dao.TrainTaskClusterResource.Table()).Where(dao.TrainTaskClusterResource.Columns().TaskId, trainTask.Id).Data(trainTask.ClusterResource).Insert()
		if err != nil {
			return err
		}
		for i, volumeMount := range trainTask.VolumeMounts {
			volumeMount.TaskId = uint(id)
			volumeMount.Name = fmt.Sprintf(VolumeMountName, i)
		}
		_, err = tx.Model(dao.TrainTaskVolumeMount.Table()).Where(dao.TrainTaskVolumeMount.Columns().TaskId, trainTask.Id).Data(trainTask.VolumeMounts).Insert()
		if err != nil {
			return err
		}
		return nil
	})
	return err
}

func (this *sTrainTask) Update(ctx context.Context, trainTask *dto.TrainTask) (err error) {
	user := service.BizCtx().Get(ctx).User
	trainTask.UpdatedByUserName = user.Username
	trainTask.UpdatedByEmployeeNo = user.EmployeeNo
	trainTaskEntity := toTrainTaskEntity(trainTask)
	if trainTaskEntity == nil {
		return fmt.Errorf("invalid train task")
	}
	err = dao.TrainTask.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		result, err := tx.Model(dao.TrainTask.Table()).
			Where(dao.TrainTask.Columns().Id, trainTask.Id).Data(trainTaskEntity).
			FieldsEx(dao.TrainTask.Columns().CreatedAt,
				dao.TrainTask.Columns().CreatedByUserName,
				dao.TrainTask.Columns().CreatedByEmployeeNo,
				dao.TrainTask.Columns().LastStatus,
				dao.TrainTask.Columns().TeamId,
				dao.TrainTask.Columns().TeamName,
				dao.TrainTask.Columns().TriggerCount,
				dao.TrainTask.Columns().CompleteCount).Update()
		if err != nil {
			return err
		}
		if cnt, err := result.RowsAffected(); err != nil || cnt == 0 {
			return fmt.Errorf("train task not found")
		}

		if trainTask.TaskType == "YAML" {
			return nil
		}

		_, err = tx.Model(dao.TrainTaskClusterResource.Table()).Where(dao.TrainTaskClusterResource.Columns().TaskId, trainTask.Id).Delete()
		if err != nil {
			return err
		}
		_, err = tx.Model(dao.TrainTaskVolumeMount.Table()).Where(dao.TrainTaskVolumeMount.Columns().TaskId, trainTask.Id).Delete()
		if err != nil {
			return err
		}
		trainTask.ClusterResource.TaskId = trainTask.Id
		_, err = tx.Model(dao.TrainTaskClusterResource.Table()).Where(dao.TrainTaskClusterResource.Columns().TaskId, trainTask.Id).Data(trainTask.ClusterResource).Insert()
		if err != nil {
			return err
		}
		for i, volumeMount := range trainTask.VolumeMounts {
			volumeMount.TaskId = trainTask.Id
			volumeMount.Name = fmt.Sprintf(VolumeMountName, i)
		}
		_, err = tx.Model(dao.TrainTaskVolumeMount.Table()).Where(dao.TrainTaskVolumeMount.Columns().TaskId, trainTask.Id).Data(trainTask.VolumeMounts).Insert()
		if err != nil {
			return err
		}
		return nil
	})
	return err
}

func (this *sTrainTask) Delete(ctx context.Context, id uint) (err error) {
	err = dao.TrainTask.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		_, err = tx.Model(dao.TrainTask.Table()).Where(dao.TrainTask.Columns().Id, id).Delete()
		if err != nil {
			return err
		}
		//_, err = tx.Model(dao.TrainTaskExecution.Table()).Where(dao.TrainTaskExecution.Columns().TaskId, id).Delete()
		//if err != nil {
		//	return err
		//}
		_, err = tx.Model(dao.TrainTaskClusterResource.Table()).Where(dao.TrainTaskClusterResource.Columns().TaskId, id).Delete()
		if err != nil {
			return err
		}
		_, err = tx.Model(dao.TrainTaskVolumeMount.Table()).Where(dao.TrainTaskVolumeMount.Columns().TaskId, id).Delete()
		if err != nil {
			return err
		}
		return nil
	})
	return
}

func (this *sTrainTask) Trigger(ctx context.Context, id uint, triggerSource string, triggeredByUserName string, triggeredByEmployeeNo string) (err error) {
	trainTask := &entity.TrainTask{}
	err = dao.TrainTask.Ctx(ctx).Where(dao.TrainTask.Columns().Id, id).Scan(trainTask)
	if err != nil {
		return err
	}
	return trigger(ctx, trainTask, triggerSource, triggeredByUserName, triggeredByEmployeeNo)
}

func trigger(ctx context.Context, trainTask *entity.TrainTask, triggerSource string, triggeredByUserName string, triggeredByEmployeeNo string) (err error) {

	// 检查train_task
	if trainTask.LastStatus == "PENDING" || trainTask.LastStatus == "RUNNING" {
		return fmt.Errorf("当前训练任务的状态是 %s, 请不要重复触发", trainTask.LastStatus)
		//return fmt.Errorf("train task status is %s, can not duplicate trigger", trainTask.LastStatus)
	}
	trainTaskExecution := toTrainTaskExecution(trainTask)
	trainTaskExecution.TriggerSource = triggerSource
	trainTaskExecution.TriggeredByUserName = triggeredByUserName
	trainTaskExecution.TriggeredByEmployeeNo = triggeredByEmployeeNo
	err = dao.TrainTaskExecution.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		rayjob := &rayv1.RayJob{}
		if trainTask.TaskType == "YAML" {
			err := json.Unmarshal([]byte(trainTask.TaskYaml), rayjob)
			if err != nil {
				log.L.WithName("trigger").Errorf(ctx, "unmarshal rayjob error:%s", err.Error())
				return err
			}
		}

		if trainTask.TaskType == "FORM" {
			// 查询train_task的cluster_resource
			clusterResource := &entity.TrainTaskClusterResource{}
			err = dao.TrainTaskClusterResource.Ctx(ctx).Where(dao.TrainTaskClusterResource.Columns().TaskId, trainTask.Id).Scan(clusterResource)
			if err != nil {
				return err
			}
			// 查询train_task的volume_mounts
			volumeMounts := []*entity.TrainTaskVolumeMount{}
			err = dao.TrainTaskVolumeMount.Ctx(ctx).Where(dao.TrainTaskVolumeMount.Columns().TaskId, trainTask.Id).Scan(&volumeMounts)
			if err != nil {
				return err
			}
			// 创建rayjob
			rayjob = toRayjob(trainTask, trainTaskExecution.ExecutionName, clusterResource, volumeMounts)
		}
		err = client.CreateRayJob(ctx, trainTask.ClusterName, rayjob)
		if err != nil {
			return err
		}

		// wait
		time.Sleep(3 * time.Second)

		rayjob, err := client.GetRayJob(ctx, trainTask.ClusterName, trainTask.Namespace, trainTaskExecution.ExecutionName)
		if err != nil {
			return err
		}
		raycluster := rayjob.Status.RayClusterName
		trainTaskExecution.DashboardUrl = fmt.Sprintf("http://ray.ttyuyin.com:8265/%s/#/overview", raycluster)

		// 插入train_task_execution
		_, err = tx.Model(dao.TrainTaskExecution.Table()).Data(trainTaskExecution).Insert()
		if err != nil {
			return err
		}
		// 更新train_task的last_status和trigger_count
		trainTask.LastStatus = "PENDING"
		_, err = tx.Model(dao.TrainTask.Table()).Where(dao.TrainTask.Columns().Id, trainTask.Id).Data("last_status", trainTask.LastStatus, "trigger_count", gdb.Raw("trigger_count + 1")).Update()
		if err != nil {
			return err
		}
		return nil
	})
	return err
}

func toRayjob(trainTask *entity.TrainTask, executionName string, clusterResource *entity.TrainTaskClusterResource, volumeMounts []*entity.TrainTaskVolumeMount) *rayv1.RayJob {
	rayjob := &rayv1.RayJob{}
	rayjob.Name = executionName
	rayjob.Namespace = trainTask.Namespace
	rayjob.Kind = "RayJob"
	rayjob.APIVersion = "ray.io/v1"

	mounts := []v1.VolumeMount{}
	volumes := []v1.Volume{}
	for _, volumeMount := range volumeMounts {
		mounts = append(mounts, v1.VolumeMount{
			Name:      volumeMount.Name,
			MountPath: volumeMount.MountPath,
			SubPath:   volumeMount.SubPath,
		})
		if volumeMount.VolumeType == "pvc" {
			volumes = append(volumes, v1.Volume{
				Name: volumeMount.Name,
				VolumeSource: v1.VolumeSource{
					PersistentVolumeClaim: &v1.PersistentVolumeClaimVolumeSource{
						ClaimName: volumeMount.VolumeName,
					},
				},
			})
		}
		if volumeMount.VolumeType == "secret" {
			volumes = append(volumes, v1.Volume{
				Name: volumeMount.Name,
				VolumeSource: v1.VolumeSource{
					Secret: &v1.SecretVolumeSource{
						SecretName: volumeMount.VolumeName,
					},
				},
			})
		}
		if volumeMount.VolumeType == "configMap" {
			volumes = append(volumes, v1.Volume{
				Name: volumeMount.Name,
				VolumeSource: v1.VolumeSource{
					ConfigMap: &v1.ConfigMapVolumeSource{
						LocalObjectReference: v1.LocalObjectReference{
							Name: volumeMount.VolumeName,
						},
					},
				},
			})
		}
	}
	replicas := int32(0)
	minReplicas := int32(clusterResource.MinReplicas)
	maxReplicas := int32(clusterResource.MaxReplicas)
	enableInTreeAutoscaling := true

	// parse resources
	resourceRequestMap := make(map[string]resource.Quantity)
	resourceRequestMap["cpu"] = resource.MustParse(clusterResource.RequestCpu)
	resourceRequestMap["memory"] = resource.MustParse(clusterResource.RequestMemory)
	resourceRequestMap[GpuCoreKey] = resource.MustParse(clusterResource.RequestGpuCore)
	resourceRequestMap[GpuMemoryKey] = resource.MustParse(clusterResource.RequestGpuMemory)
	resourceLimitMap := make(map[string]resource.Quantity)
	resourceLimitMap["cpu"] = resource.MustParse(clusterResource.LimitCpu)
	resourceLimitMap["memory"] = resource.MustParse(clusterResource.LimitMemory)
	resourceLimitMap[GpuCoreKey] = resource.MustParse(clusterResource.LimitGpuCore)
	resourceLimitMap[GpuMemoryKey] = resource.MustParse(clusterResource.LimitGpuMemory)
	var clusterType string
	if strings.HasPrefix(trainTask.ClusterName, "k8s-tc") {
		clusterType = "tencent"
	} else if strings.HasPrefix(trainTask.ClusterName, "k8s-ali") {
		clusterType = "aliyun"
	} else if strings.HasPrefix(trainTask.ClusterName, "k8s-hw") {
		clusterType = "huawei"
	} else if strings.HasPrefix(trainTask.ClusterName, "k8s-hs") {
		clusterType = "volcengine"
	}
	converter := gpu.GetStrategy(clusterType)
	if converter != nil {
		converter.Convert(resourceRequestMap)
		converter.Convert(resourceLimitMap)
	}

	rayjob.Spec = rayv1.RayJobSpec{
		Entrypoint: trainTask.StartCmd,
		RayClusterSpec: &rayv1.RayClusterSpec{
			HeadServiceAnnotations: map[string]string{
				"managed-by": "kuberay-dynamic-route",
			},
			EnableInTreeAutoscaling: &enableInTreeAutoscaling,
			AutoscalerOptions: &rayv1.AutoscalerOptions{
				Resources: &v1.ResourceRequirements{
					Requests: v1.ResourceList{
						v1.ResourceCPU:    resource.MustParse("1"),
						v1.ResourceMemory: resource.MustParse("2Gi"),
					},
					Limits: v1.ResourceList{
						v1.ResourceCPU:    resource.MustParse("2"),
						v1.ResourceMemory: resource.MustParse("4Gi"),
					},
				},
			},
			HeadGroupSpec: rayv1.HeadGroupSpec{
				RayStartParams: map[string]string{
					"num-gpus": "0",
				},
				Template: v1.PodTemplateSpec{
					Spec: v1.PodSpec{
						Containers: []v1.Container{
							{
								Name:  "ray-head",
								Image: trainTask.ImageUrl,
								Ports: []v1.ContainerPort{
									{
										ContainerPort: 6379,
										Name:          "gcs-server",
									},
									{
										ContainerPort: 8265,
										Name:          "dashboard",
									},
									{
										ContainerPort: 10001,
										Name:          "client",
									},
								},
								Resources: v1.ResourceRequirements{
									Requests: v1.ResourceList{
										v1.ResourceCPU:    resource.MustParse("1"),
										v1.ResourceMemory: resource.MustParse("2Gi"),
									},
									Limits: v1.ResourceList{
										v1.ResourceCPU:    resource.MustParse("2"),
										v1.ResourceMemory: resource.MustParse("4Gi"),
									},
								},
								VolumeMounts: mounts,
							},
						},
						Volumes: volumes,
					},
				},
			},
			WorkerGroupSpecs: []rayv1.WorkerGroupSpec{
				{
					RayStartParams: map[string]string{
						"num-gpus": "1",
					},
					GroupName:   "gpu-group",
					Replicas:    &replicas,
					MinReplicas: &minReplicas,
					MaxReplicas: &maxReplicas,
					Template: v1.PodTemplateSpec{
						Spec: v1.PodSpec{
							Containers: []v1.Container{
								{
									Name:  "ray-worker",
									Image: trainTask.ImageUrl,
									Resources: v1.ResourceRequirements{
										Requests: convertResourceMap(resourceRequestMap),
										Limits:   convertResourceMap(resourceLimitMap),
									},
									VolumeMounts: mounts,
								},
							},
							Volumes: volumes,
							Tolerations: []v1.Toleration{
								{
									Key:      "pool-type",
									Operator: "Equal",
									Value:    "gpu",
									Effect:   "NoSchedule",
								},
							},
						},
					},
				},
			},
		},
	}
	if clusterResource.GpuType != "" {
		rayjob.Spec.RayClusterSpec.WorkerGroupSpecs[0].Template.Spec.Affinity = &v1.Affinity{
			NodeAffinity: &v1.NodeAffinity{
				RequiredDuringSchedulingIgnoredDuringExecution: &v1.NodeSelector{
					NodeSelectorTerms: []v1.NodeSelectorTerm{
						{
							MatchExpressions: []v1.NodeSelectorRequirement{
								{
									Key:      "gpu-type",
									Operator: "In",
									Values:   []string{clusterResource.GpuType},
								},
							},
						},
					},
				},
			},
		}
	}
	return rayjob
}

func convertResourceMap(input map[string]resource.Quantity) map[v1.ResourceName]resource.Quantity {
	output := make(map[v1.ResourceName]resource.Quantity)
	for k, v := range input {
		output[v1.ResourceName(k)] = v
	}
	return output
}

func toTrainTaskExecution(trainTask *entity.TrainTask) *entity.TrainTaskExecution {
	return &entity.TrainTaskExecution{
		TaskId:        trainTask.Id,
		TeamId:        trainTask.TeamId,
		TeamName:      trainTask.TeamName,
		ExecutionName: fmt.Sprintf(ExecutionName, trainTask.Id, grand.Str(LowerLetters, 8)),
		ClusterName:   trainTask.ClusterName,
		ClusterId:     trainTask.ClusterId,
		Namespace:     trainTask.Namespace,
		ImageUrl:      trainTask.ImageUrl,
		Priority:      trainTask.Priority,
		StartTime:     gtime.Now(),
		TriggerTime:   gtime.Now(),
		Status:        "PENDING",
	}
}

func toTrainTaskEntity(trainTask *dto.TrainTask) *entity.TrainTask {
	jsonStr := "{}"
	if trainTask.TaskYaml != "" {
		// check
		jsonData, err := yaml.YAMLToJSON([]byte(trainTask.TaskYaml))
		if err != nil {
			return nil
		}

		jsonStr = string(jsonData)
	}
	priority := "P1"
	if trainTask.Priority != "" {
		priority = trainTask.Priority
	}
	return &entity.TrainTask{
		Id:                  trainTask.Id,
		TaskName:            trainTask.TaskName,
		TeamId:              trainTask.TeamId,
		TeamName:            trainTask.TeamName,
		ClusterName:         trainTask.ClusterName,
		ClusterId:           trainTask.ClusterId,
		Namespace:           trainTask.Namespace,
		ImageUrl:            trainTask.ImageUrl,
		StartCmd:            trainTask.StartCmd,
		Priority:            priority,
		TaskType:            trainTask.TaskType,
		TaskYaml:            jsonStr,
		CreatedByEmployeeNo: trainTask.CreatedByEmployeeNo,
		CreatedByUserName:   trainTask.CreatedByUserName,
		UpdatedByEmployeeNo: trainTask.UpdatedByEmployeeNo,
		UpdatedByUserName:   trainTask.UpdatedByUserName,
	}
}
