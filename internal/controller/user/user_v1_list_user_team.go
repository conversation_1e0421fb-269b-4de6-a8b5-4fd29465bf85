package user

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"mlops/internal/model/dto"
	"mlops/internal/service"

	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"

	"mlops/api/user/v1"
)

func (c *ControllerV1) ListUserTeam(ctx context.Context, req *v1.ListUserTeamReq) (res *v1.ListUserTeamRes, err error) {
	res = &v1.ListUserTeamRes{}
	targetTeams := make([]*dto.TeamRole, 0)
	targetMaps := make(map[string]*dto.TeamRole)

	profile, err := service.PlatFormAuth().GetUserAuthProfile(ctx, g.RequestFromCtx(ctx).Get("pid").Int())
	if err != nil {
		return nil, err
	}

	for _, teamRole := range profile.TeamRoles {
		targetTeams = append(targetTeams, &dto.TeamRole{
			Id:       teamRole.Id,
			TeamId:   teamRole.TeamId,
			TeamName: teamRole.TeamName,
			Role:     teamRole.Role,
		})
	}

	lists, err := service.Team().List(ctx)
	if err != nil {
		return nil, err
	}

	return nil, gerror.NewCode(gcode.CodeNotImplemented)
}
