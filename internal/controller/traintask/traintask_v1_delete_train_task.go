package traintask

import (
	"context"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/log"
	"mlops/internal/model/dto"
	"strconv"

	"mlops/api/traintask/v1"
	"mlops/internal/service"

	"github.com/gogf/gf/v2/frame/g"
)

func (c *ControllerV1) DeleteTrainTask(ctx context.Context, req *v1.DeleteTrainTaskReq) (res *v1.DeleteTrainTaskRes, err error) {
	res = &v1.DeleteTrainTaskRes{}
	r := g.RequestFromCtx(ctx).GetRouterMap()["id"]
	idInt, err := strconv.Atoi(r)
	if err != nil {
		return nil, err
	}
	err = service.TrainTask().Delete(ctx, uint(idInt))
	if err != nil {
		return nil, err
	}

	trainTaskExecutions, err := service.TrainTaskExecution().List(ctx, dto.TrainTaskExecutionListInput{TaskId: uint(idInt)})
	if err != nil {
		return nil, err
	}

	for _, execution := range trainTaskExecutions {
		err := service.TrainTaskExecution().Interrupt(ctx, execution.Id)
		if err != nil {
			log.L.WithName("DeleteTrainTask").Errorf(ctx, "interrupt train task execution:%d, error:%s",
				execution.Id, err.Error())
		}
	}

	return res, nil
}
