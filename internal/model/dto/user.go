package dto

import authConsts "mlops/internal/consts/auth"

type UserLoginInput struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

type UserProfile struct {
	Uid         int64                 `json:"uid"`
	Username    string                `json:"username"`
	ChineseName string                `json:"chineseName"`
	Email       string                `json:"email"`
	EmployeeNo  string                `json:"employeeNo"`
	CreatedAt   int64                 `json:"createdAt"`
	Role        []authConsts.RoleKind `json:"role"`
	TeamRoles   []TeamRole            `json:"team"`
}

type UserViewObj struct {
	Id          int64                 `json:"id"`
	Uid         string                `json:"uid"`
	Name        string                `json:"name"`
	ChineseName string                `json:"chineseName"`
	Email       string                `json:"email"`
	EmployeeNo  string                `json:"employeeNo"`
	Role        []authConsts.RoleKind `json:"role"`
}

type UserTeamRole struct {
	TeamId   string                  `json:"teamId"`
	UserId   int64                   `json:"userId"`
	Category string                  `json:"category"`
	Role     authConsts.TeamRoleKind `json:"role"`
}

type Role struct {
	Id   int64  `json:"id"`
	Name string `json:"name"`
	Desc string `json:"desc"`
}
