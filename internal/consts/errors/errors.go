package errors

import (
	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
)

var (
	//ErrInvalidUserOrPassword = apierrors.New("无效的用户或密码", InvalidUserOrPassword)
	ErrInvalidUserOrPassword = gerror.NewCode(gcode.CodeValidationFailed, "无效的用户或密码")
	ErrUserNotFound          = gerror.NewCode(gcode.CodeNotFound, "当前用户不存在")
	ErrTaskNotFound          = gerror.NewCode(gcode.CodeBusinessValidationFailed, "当前任务不存在")
)
