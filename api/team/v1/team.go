package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"mlops/internal/model/dto"
)

type ListClusterNamespaceReq struct {
	g.Meta `path:"/team/cluster-namespace/list" method:"get" tags:"Team" sm:"list cluster namespace"`
	TeamId int `json:"teamId" v:"required#teamId is required"`
}

type ListClusterNamespaceRes struct {
	ClusterNamespaces []*dto.ClusterNamespace `json:"clusterNamespaces"`
}

type ListTeamReq struct {
	g.Meta `path:"/team/list" method:"get" tags:"Team" sm:"list team"`
}

type ListTeamRes struct {
	Teams []*dto.Team `json:"teams"`
}

type ListTeamUserReq struct {
	g.Meta `path:"/team/user/list/:pid" tags:"Team" method:"get" summary:"team's user list'"`
}

type ListTeamUserRes struct {
	Users []*dto.UserTeamRole `json:"users"`
}
