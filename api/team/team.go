// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package team

import (
	"context"

	"mlops/api/team/v1"
)

type ITeamV1 interface {
	ListClusterNamespace(ctx context.Context, req *v1.ListClusterNamespaceReq) (res *v1.ListClusterNamespaceRes, err error)
	ListTeam(ctx context.Context, req *v1.ListTeamReq) (res *v1.ListTeamRes, err error)
	ListTeamUser(ctx context.Context, req *v1.ListTeamUserReq) (res *v1.ListTeamUserRes, err error)
}
