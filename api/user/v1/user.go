package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"mlops/internal/consts/auth"
	"mlops/internal/model/dto"
	"mlops/tools/common"
)

type SetAdminReq struct {
	g.Meta  `path:"/user/set-admin" method:"post" tags:"User" sm:"set user admin" role:"admin,consoleAdmin"`
	UserId  int  `json:"userId" v:"required#Require user id" dc:"user id"`
	IsAdmin bool `json:"isAdmin" v:"required#Require is_admin" dc:"is admin"`
}

type SetAdminRes struct {
}

type SetConsoleAdminReq struct {
	g.Meta         `path:"/user/set-console-admin" method:"post" tags:"User" sm:"set user console admin" role:"admin,consoleAdmin"`
	UserId         int  `json:"userId" v:"required#Require user id" dc:"user id"`
	IsConsoleAdmin bool `json:"isConsoleAdmin" v:"required#Require is_Console_admin" dc:"is consoleAdmin"`
}

type SetConsoleAdminRes struct {
}

type SetTeamAuthReq struct {
	g.Meta       `path:"/user/set-team-auth" method:"post" tags:"User" sm:"set user team auth"`
	UserId       int               `json:"userId" v:"required#Require user id" dc:"user id"`
	TeamId       int               `json:"teamId" v:"required#Require team id" dc:"team id"`
	TeamRoleKind auth.TeamRoleKind `json:"teamRoleKind"`
}

type SetTeamAuthRes struct {
}

type UnSetTeamAuthReq struct {
	g.Meta       `path:"/user/unset-team-auth" method:"post" tags:"User" sm:"unset user team auth"`
	UserId       int               `json:"userId" v:"required#Require user id" dc:"user id"`
	TeamId       int               `json:"teamId" v:"required#Require team id" dc:"team id"`
	TeamRoleKind auth.TeamRoleKind `json:"teamRoleKind"`
}

type UnSetTeamAuthRes struct {
}

type UserProfileReq struct {
	g.Meta `path:"/user/profile" method:"get" tags:"User" sm:"get user profile"`
}

type UserProfileRes struct {
	*dto.UserProfile
}

type GetUserProfileReq struct {
	g.Meta `path:"/user/profile/get/:uid" method:"get" tags:"User" sm:"get user profile" role:"admin,consoleAdmin"`
}

type GetUserProfileRes struct {
	*dto.UserProfile
}

type ListUserReq struct {
	g.Meta `path:"/user/list" method:"get" tags:"User" sm:"list user"`
	Page   int    `json:"page" d:"1"`
	Size   int    `json:"size" d:"10"`
	Search string `json:"search"`
}

type ListUserRes struct {
	*common.ListPageRes
}

type SyncTeamReq struct {
	g.Meta `path:"/user/sync-team" method:"post" tags:"User" sm:"sync team" role:"admin,consoleAdmin"`
}

type SyncTeamRes struct {
}

/*
	获取用户拥有的团队列表
*/

type ListUserWithTeamIdReq struct {
	g.Meta `path:"/user/team/list/:pid" tags:"User" method:"get" summary:"user's team list'"`
}

type ListUserWithTeamIdRes struct {
}
