// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package user

import (
	"context"

	"mlops/api/user/v1"
)

type IUserV1 interface {
	SetAdmin(ctx context.Context, req *v1.SetAdminReq) (res *v1.SetAdminRes, err error)
	SetConsoleAdmin(ctx context.Context, req *v1.SetConsoleAdminReq) (res *v1.SetConsoleAdminRes, err error)
	SetTeamAuth(ctx context.Context, req *v1.SetTeamAuthReq) (res *v1.SetTeamAuthRes, err error)
	UnSetTeamAuth(ctx context.Context, req *v1.UnSetTeamAuthReq) (res *v1.UnSetTeamAuthRes, err error)
	UserProfile(ctx context.Context, req *v1.UserProfileReq) (res *v1.UserProfileRes, err error)
	GetUserProfile(ctx context.Context, req *v1.GetUserProfileReq) (res *v1.GetUserProfileRes, err error)
	ListUser(ctx context.Context, req *v1.ListUserReq) (res *v1.ListUserRes, err error)
	SyncTeam(ctx context.Context, req *v1.SyncTeamReq) (res *v1.SyncTeamRes, err error)
	ListUserTeam(ctx context.Context, req *v1.ListUserTeamReq) (res *v1.ListUserTeamRes, err error)
}
